from typing import Dict
import numpy as np
import pandas as pd
import sys
from joblib import Parallel, delayed

# Import cos_ai_service modules - use exact imports from the original code
from cos_ai_algorithm_interfaces import CosAIServiceAlgo, calculate_micro_f1_score

from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import optuna
data_dir_latency = './datasets/COS_avg_latency/'
data_dir_latency = Path(data_dir_latency)

data_dir_normal_error_code = './datasets/COS_normal_error_code/'
data_dir_normal_error_code = Path(data_dir_normal_error_code)

files = [f for f in data_dir_latency.iterdir() if f.is_file()] + [f for f in data_dir_normal_error_code.iterdir() if f.is_file()]
files.sort(key=lambda x: int(x.name.split('.')[0]) if x.name.split('.')[0].isdigit() else float('inf'))

def flag_calculation(file, params):
    cur = pd.read_csv(file)
    alg_instance = CosAIServiceAlgo(params=params)

    test_len = cur['change_status'].sum()
    alg_instance.test_phase_serialize(cur, test_len=test_len, is_filter=True)

    cur['now_prediction'] = alg_instance.anomaly_flag()

    cur['now_score'] = alg_instance.anomaly_score()

    cur = cur[cur['change_status']==1]

    return cur['now_prediction'].values, cur['now_score'].values, cur['prior_prediction'].values, cur['is_anomaly'].values

def objective(trial):
    # Suggest parameters to tune
    params = {
        "param_list": {
            "param_sparse": 0.80,
            "param_dtw_low": trial.suggest_float("param_dtw_low", 0.01, 0.1, step=0.001),
            "param_dtw_high": trial.suggest_float("param_dtw_high", 0.15, 0.25, step=0.001),
            "param_percent": trial.suggest_float("param_percent", 99, 100, step=0.1),
            "time_window_focus": trial.suggest_int("time_window_focus", 3, 10),
            "time_window_dtw": trial.suggest_int("time_window_dtw", 1440, 4320, step=1440),
            "param_dtw": trial.suggest_float("param_dtw", 0.01, 0.3, step=0.01),
        }
    }

    result_list = Parallel(n_jobs=32, backend='multiprocessing')(delayed(flag_calculation)(file, params) for file in files)

    now_preds_list = []
    now_scores_list = []
    prior_preds_list = []
    labels_list = []
    for result in result_list:
        now_preds_list.append(result[0])
        now_scores_list.append(result[1])
        prior_preds_list.append(result[2])
        labels_list.append(result[3])

    scores = calculate_micro_f1_score(now_preds_list, labels_list, mode="raw")
    return scores['F1-score'], scores['Precision'], scores['Recall']

init_params_1 = {
    'param_dtw_low': 0.049, 
    'param_dtw_high': 0.184, 
    'param_percent': 99.0, 
    'time_window_focus': 3, 
    'time_window_dtw': 1440, 
    'param_dtw': 0.14,
}

init_params_2 = {
    "param_dtw_low": 0.054,
    "param_dtw_high": 0.197,
    "param_percent": 99.1,
    "time_window_focus": 6,
    "time_window_dtw": 1440,
    "param_dtw": 0.23,
}

sampler = optuna.samplers.TPESampler(seed=0)
study = optuna.create_study(directions=['maximize']*3, sampler=sampler)
study.enqueue_trial(init_params_1)
study.enqueue_trial(init_params_2)
study.optimize(objective, n_trials=200, n_jobs=1)

trial_with_highest_reward = max(study.best_trials, key=lambda t: t.values[0])

print('Best Trial:', trial_with_highest_reward)
print("Best Parameters:", trial_with_highest_reward.params)
print("Best Values (F1, Precision, Recall):", trial_with_highest_reward.values)